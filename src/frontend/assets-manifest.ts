// Asset manifest for HTML meta tags
// This ensures all assets are processed by Vite and available via /assets/ path

import favicon from '@/assets/images/favicon.ico'
import faviconSvg from '@/assets/images/favicon.svg'
import appleTouchIcon from '@/assets/images/apple-touch-icon.png'
import siteWebmanifest from '@/assets/images/site.webmanifest'
import ogImage from '@/assets/images/og-image.png'
import webAppManifest192 from '@/assets/images/web-app-manifest-192x192.png'
import webAppManifest512 from '@/assets/images/web-app-manifest-512x512.png'
import logoLightLg from '@/assets/images/logo-light-lg.png'
import logoDarkLg from '@/assets/images/logo-dark-lg.png'

export const assets = {
  favicon,
  faviconSvg,
  appleTouchIcon,
  siteWebmanifest,
  ogImage,
  webAppManifest192,
  webAppManifest512,
  logoLightLg,
  logoDarkLg,
}

// Make assets available globally for HTML meta tag updates
if (typeof window !== 'undefined') {
  ((window as unknown) as Window & { __ASSETS__: typeof assets }).__ASSETS__ = assets
}
