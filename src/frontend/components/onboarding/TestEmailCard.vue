<template>
  <div class="card bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20">
    <div class="card-body">
      <h3 class="card-title text-primary">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
        Try it instantly!
      </h3>
      
      <p class="text-base-content/80 mb-4">
        Send an email to your personal test address and see the webhook payload in real-time:
      </p>
      
      <div class="bg-base-100 rounded-lg p-4 border border-base-300">
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <label class="label">
              <span class="label-text font-medium">Your test email address:</span>
            </label>
            <div class="flex items-center gap-2">
              <input 
                type="text" 
                :value="testEmail" 
                readonly 
                class="input input-bordered flex-1 font-mono text-sm bg-base-200"
              >
              <button 
                @click="copyToClipboard"
                class="btn btn-outline btn-sm"
                :class="{ 'btn-success': copied }"
              >
                <svg v-if="!copied" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M5 13l4 4L19 7" />
                </svg>
                {{ copied ? 'Copied!' : 'Copy' }}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="mt-4 space-y-2">
        <div class="flex items-center gap-2 text-sm text-base-content/70">
          <span class="w-2 h-2 bg-success rounded-full"></span>
          <span>Emails appear in the <router-link to="/logs" class="link link-primary">Logs</router-link> tab instantly</span>
        </div>
        <div class="flex items-center gap-2 text-sm text-base-content/70">
          <span class="w-2 h-2 bg-info rounded-full"></span>
          <span>View the full webhook JSON payload</span>
        </div>
        <div class="flex items-center gap-2 text-sm text-base-content/70">
          <span class="w-2 h-2 bg-warning rounded-full"></span>
          <span>No setup required - works immediately</span>
        </div>
      </div>
      
      <div class="card-actions justify-end mt-4">
        <router-link to="/logs" class="btn btn-primary btn-sm">
          View logs
          <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M9 5l7 7-7 7" />
          </svg>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useToast } from '@composables/useToast'

// Props
interface Props {
  userId?: string
}

const props = withDefaults(defineProps<Props>(), {
  userId: ''
})

// State
const copied = ref(false)
const { success } = useToast()

// Computed
const testEmail = computed(() => {
  if (!props.userId) return 'Loading...'
  
  // Get last 8 characters of user ID
  const suffix = props.userId.slice(-8)
  return `${suffix}@web.xadi.eu`
})

// Methods
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(testEmail.value)
    copied.value = true
    success('Test email address copied to clipboard!')
    
    // Reset copied state after 2 seconds
    setTimeout(() => {
      copied.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = testEmail.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    
    copied.value = true
    success('Test email address copied to clipboard!')
    setTimeout(() => {
      copied.value = false
    }, 2000)
  }
}

onMounted(() => {
  // Auto-focus and select the email input for easy copying
  // This is optional UX enhancement
})
</script>

<style scoped>
.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.input[readonly] {
  cursor: text;
}

.input[readonly]:focus {
  outline: none;
  border-color: var(--fallback-bc, oklch(var(--bc) / 0.2));
}
</style>
