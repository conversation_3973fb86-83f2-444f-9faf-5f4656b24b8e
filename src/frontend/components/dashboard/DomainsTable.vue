<script setup lang="ts">
import { computed } from 'vue'
import DataTable from '@components/ui/DataTable.vue'
import type { TableColumn } from '@components/ui/DataTable.vue'
import type { Domain } from '@types'
import { formatWebhookUrl } from '../../utils/url'

interface Props {
  domains: Domain[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  toggleDomain: [domainId: string, active: boolean]
  verifyDomain: [domainId: string]
  editDomain: [domain: Domain]
  deleteDomain: [domainId: string, domainName: string]
  showAliases: [domainId: string]
  viewLogs: [domainId: string]
}>()

const columns: TableColumn<Domain>[] = [
  {
    key: 'domainName',
    label: 'Domain',
    sortable: true,
    render: (value, row) => {
      const isDisabled = row.verificationStatus === 'PENDING'
      const toggleClass = isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'
      const domainName = value || row.domain

      return `
        <div class="flex items-center space-x-3">
          <input type="checkbox"
                 class="toggle toggle-primary domain-status-toggle ${toggleClass}"
                 data-domain-id="${row.id}"
                 ${row.active ? 'checked' : ''}
                 ${isDisabled ? 'disabled' : ''}>
          <div class="text-sm font-medium text-base-content">${domainName}</div>
        </div>
      `
    }
  },
  {
    key: 'verificationStatus',
    label: 'Status',
    sortable: true,
    render: (value, row) => {
      const statusConfig = {
        'VERIFIED': {
          statusClass: 'status status-success',
          text: 'Verified',
          textClass: 'text-success'
        },
        'PENDING': {
          statusClass: 'status status-info animate-bounce',
          text: 'Verify now',
          textClass: 'text-info cursor-pointer underline hover:text-info-focus',
          clickable: true
        },
        'FAILED': {
          statusClass: 'status status-error animate-ping',
          text: 'Error',
          textClass: 'text-error'
        }
      }
      
      const config = statusConfig[value] || statusConfig['PENDING']
      const clickHandler = config.clickable ? `onclick="window.openModal('domain-verification', { domainId: '${row.id}', domain: '${row.domainName || row.domain}' })"` : ''

      return `
        <div class="flex items-center gap-2" ${clickHandler}>
          <div class="${config.statusClass}"></div>
          <span class="${config.textClass}">${config.text}</span>
        </div>
      `
    }
  },
  {
    key: 'webhook',
    label: 'Webhook',
    sortable: true,
    render: (value) => {
      if (!value?.url) return '<span class="text-base-content/60">No webhook</span>'

      const verificationBadge = value.verified
        ? '<span class="badge badge-success bg-primary/10 text-base-content badge-sm ml-2"><div class="status status-success mr-1"></div>Verified</span>'
        : '<span class="badge badge-warning badge-sm ml-2 cursor-pointer underline hover:badge-warning/80" onclick="window.openModal(\'webhook-verification\', { webhookId: \'' + value.id + '\', webhookUrl: \'' + value.url + '\' })"><div class="status status-info mr-1"></div>Verify now</span>'

      const displayUrl = formatWebhookUrl(value.url, 40)

      return `
        <div class="flex items-center gap-2 tooltip" data-tip="${value.url}">
          <span class="text-sm text-base-content">${displayUrl}</span>
          ${verificationBadge}
        </div>
      `
    }
  },
  {
    key: 'aliases',
    label: 'Aliases',
    sortable: true,
    align: 'center',
    render: (value, row) => {
      const count = value?.length || 0
      
      if (count > 0) {
        return count
      }
      
      return '<span class="text-base-content/60">0</span>'
    }
  },
  {
    key: 'actions',
    label: '',
    width: '200px',
    render: (value, row) => {
      return `
        <div class="flex items-center justify-end space-x-1">
          <button type="button"
                  class="btn btn-outline btn-secondary btn-xs"
                  data-action="editDomain"
                  data-domain-id="${row.id}">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit
          </button>
          <button type="button"
                  class="btn btn-outline btn-primary btn-xs"
                  data-action="viewLogs"
                  data-domain-id="${row.id}">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Logs
          </button>
          <button type="button"
                  class="btn btn-outline btn-error btn-xs"
                  data-action="deleteDomain"
                  data-domain-id="${row.id}"
                  data-domain-name="${row.domainName || row.domain}">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete
          </button>
        </div>
      `
    }
  }
]

const handleRowClick = (domain: Domain, index: number) => {
  // Handle row click if needed
}

const handleActionClick = (event: Event) => {
  const target = event.target as HTMLElement
  const button = target.closest('button[data-action]') as HTMLButtonElement

  if (!button) return

  const action = button.dataset.action
  const domainId = button.dataset.domainId
  const domainName = button.dataset.domainName

  if (action === 'editDomain' && domainId) {
    const domain = props.domains.find(d => d.id === domainId)
    if (domain) {
      emit('editDomain', domain)
    }
  } else if (action === 'viewLogs' && domainId) {
    emit('viewLogs', domainId)
  } else if (action === 'deleteDomain' && domainId && domainName) {
    emit('deleteDomain', domainId, domainName)
  }
}
</script>

<template>
  <div class="bg-base-100 border border-base-300 rounded-lg shadow-sm" @click="handleActionClick">
    <DataTable
      :columns="columns"
      :data="domains"
      :loading="loading"
      empty-message="No domains yet. Create your first domain to get started!"
      @row-click="handleRowClick"
    />
  </div>
</template>
