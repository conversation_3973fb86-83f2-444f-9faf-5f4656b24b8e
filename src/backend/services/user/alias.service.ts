import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';

// Helper function to validate email format
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Helper function to extract domain from email
function extractDomainFromEmail(email: string): string | null {
  const parts = email.split('@');
  return parts.length === 2 ? parts[1].toLowerCase() : null;
}

// Reserved email local parts that should not be allowed
const RESERVED_LOCAL_PARTS = [
  'admin', 'administrator', 'root', 'postmaster', 'webmaster',
  'hostmaster', 'abuse', 'security', 'noreply', 'no-reply',
  'mailer-daemon', 'daemon', 'system', 'api', 'www'
];

// Helper function to check if local part is reserved
function isReservedLocalPart(localPart: string): boolean {
  return RESERVED_LOCAL_PARTS.includes(localPart.toLowerCase());
}

export interface CreateAliasData {
  email: string;
  domainId: string;
  webhookId: string;
  active?: boolean;
  userId: string;
}

export interface UpdateAliasData {
  email?: string;
  webhookId?: string;
  active?: boolean;
  allowAttachments?: boolean;
  includeEnvelope?: boolean;
}

export class AliasService {
  /**
   * Get all aliases for a user
   */
  async getUserAliases(userId: string) {
    const aliases = await prisma.alias.findMany({
      where: {
        domain: {
          userId
        }
      },
      include: {
        domain: {
          select: {
            id: true,
            domain: true,
            verified: true
          }
        },
        webhook: {
          select: {
            id: true,
            name: true,
            url: true,
            verified: true,
            webhookSecret: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const result = aliases.map(alias => ({
      id: alias.id,
      email: alias.email,
      active: alias.active,
      createdAt: alias.createdAt.toISOString(),
      updatedAt: alias.updatedAt.toISOString(),
      domain: alias.domain,
      webhook: {
        id: alias.webhook.id,
        name: alias.webhook.name,
        url: alias.webhook.url,
        verified: alias.webhook.verified,
        hasSecret: !!alias.webhook.webhookSecret
      }
    }));

    return {
      aliases: result,
      total: result.length
    };
  }

  /**
   * Get a specific alias by ID for a user
   */
  async getAliasById(aliasId: string, userId: string) {
    const alias = await prisma.alias.findFirst({
      where: {
        id: aliasId,
        domain: {
          userId
        }
      },
      include: {
        domain: {
          select: {
            id: true,
            domain: true,
            verified: true
          }
        },
        webhook: {
          select: {
            id: true,
            name: true,
            url: true,
            verified: true,
            webhookSecret: true
          }
        }
      }
    });

    if (!alias) {
      return null;
    }

    return {
      id: alias.id,
      email: alias.email,
      active: alias.active,
      createdAt: alias.createdAt.toISOString(),
      updatedAt: alias.updatedAt.toISOString(),
      domain: alias.domain,
      webhook: {
        id: alias.webhook.id,
        name: alias.webhook.name,
        url: alias.webhook.url,
        verified: alias.webhook.verified,
        hasSecret: !!alias.webhook.webhookSecret
      }
    };
  }

  /**
   * Create a new alias
   */
  async createAlias(data: CreateAliasData) {
    // Validate email format
    if (!isValidEmail(data.email)) {
      throw new Error('Invalid email format');
    }

    const emailDomain = extractDomainFromEmail(data.email);
    if (!emailDomain) {
      throw new Error('Could not extract domain from email');
    }

    const localPart = data.email.split('@')[0];

    // Check for reserved local parts
    if (isReservedLocalPart(localPart)) {
      throw new Error(`The local part '${localPart}' is reserved and cannot be used for aliases`);
    }

    // Check if domain exists and belongs to user
    const domain = await prisma.domain.findFirst({
      where: {
        id: data.domainId,
        userId: data.userId
      }
    });

    if (!domain) {
      throw new Error('Domain not found');
    }

    // Verify email domain matches the domain record
    if (emailDomain !== domain.domain) {
      throw new Error(`Email domain '${emailDomain}' does not match domain '${domain.domain}'`);
    }

    // Critical: Domain must be verified to create aliases
    if (!domain.verified) {
      throw new Error('Cannot create aliases for unverified domains. Please verify domain ownership first.');
    }

    // Check if webhook exists and belongs to user
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: data.webhookId,
        userId: data.userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Check if alias already exists
    const existingAlias = await prisma.alias.findFirst({
      where: {
        email: data.email,
        domainId: data.domainId
      }
    });

    if (existingAlias) {
      throw new Error('Alias with this email already exists for this domain');
    }

    // Create the alias - set active=false if webhook is unverified
    const aliasActive = webhook.verified ? (data.active ?? true) : false

    const alias = await prisma.alias.create({
      data: {
        email: data.email,
        domainId: data.domainId,
        webhookId: data.webhookId,
        active: aliasActive
      }
    });

    return {
      success: true,
      alias: {
        id: alias.id,
        email: alias.email,
        active: alias.active,
        createdAt: alias.createdAt.toISOString(),
        updatedAt: alias.updatedAt.toISOString(),
        domainId: alias.domainId,
        webhookId: alias.webhookId
      },
      webhook: {
        id: webhook.id,
        name: webhook.name,
        url: webhook.url,
        verified: webhook.verified
      },
      webhookNeedsVerification: !webhook.verified,
      aliasSetInactive: !webhook.verified // Indicates if alias was set inactive due to unverified webhook
    };
  }

  /**
   * Update an existing alias
   */
  async updateAlias(aliasId: string, userId: string, updates: UpdateAliasData) {
    // Check if alias exists and belongs to user
    const existingAlias = await prisma.alias.findFirst({
      where: {
        id: aliasId,
        domain: {
          userId
        }
      }
    });

    if (!existingAlias) {
      throw new Error('Alias not found');
    }

    // If webhook is being updated, verify it exists and belongs to user
    if (updates.webhookId) {
      const webhook = await prisma.webhook.findFirst({
        where: {
          id: updates.webhookId,
          userId
        }
      });

      if (!webhook) {
        throw new Error('Webhook not found');
      }
    }

    // Prepare update data
    const updateData: any = { updatedAt: new Date() };

    if (updates.email !== undefined) updateData.email = updates.email;
    if (updates.webhookId !== undefined) updateData.webhookId = updates.webhookId;
    if (updates.active !== undefined) updateData.active = updates.active;

    // Handle configuration updates
    if (updates.allowAttachments !== undefined || updates.includeEnvelope !== undefined) {
      const currentConfig = existingAlias.configuration as any || {};
      const newConfig = {
        ...currentConfig,
        ...(updates.allowAttachments !== undefined && { allowAttachments: updates.allowAttachments }),
        ...(updates.includeEnvelope !== undefined && { includeEnvelope: updates.includeEnvelope })
      };
      updateData.configuration = newConfig;
    }

    const updatedAlias = await prisma.alias.update({
      where: { id: aliasId },
      data: updateData
    });

    return {
      success: true,
      alias: {
        id: updatedAlias.id,
        email: updatedAlias.email,
        active: updatedAlias.active,
        createdAt: updatedAlias.createdAt.toISOString(),
        updatedAt: updatedAlias.updatedAt.toISOString(),
        domainId: updatedAlias.domainId,
        webhookId: updatedAlias.webhookId
      }
    };
  }

  /**
   * Delete an alias
   */
  async deleteAlias(aliasId: string, userId: string) {
    // Check if alias exists and belongs to user
    const alias = await prisma.alias.findFirst({
      where: {
        id: aliasId,
        domain: {
          userId
        }
      },
      include: {
        domain: true
      }
    });

    if (!alias) {
      throw new Error('Alias not found');
    }

    // Check if this is the only alias for the domain
    const aliasCount = await prisma.alias.count({
      where: {
        domainId: alias.domainId
      }
    });

    // Prevent deletion if this is the only alias for the domain
    if (aliasCount === 1) {
      throw new Error('Cannot delete the last alias for a domain. Each domain must have at least one alias.');
    }

    // Additional check: if this is a catch-all alias and the only one, prevent deletion
    if (alias.email.startsWith('*@') && aliasCount === 1) {
      throw new Error('Cannot delete the catch-all alias as it is the only alias for this domain.');
    }

    await prisma.alias.delete({
      where: { id: aliasId }
    });

    return {
      success: true,
      message: 'Alias deleted successfully'
    };
  }

  /**
   * Get aliases for a specific domain
   */
  async getDomainAliases(domainId: string, userId: string) {
    // Check if domain exists and belongs to user
    const domain = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!domain) {
      throw new Error('Domain not found');
    }

    // Get aliases for this domain
    const aliases = await prisma.alias.findMany({
      where: {
        domainId: domainId
      },
      include: {
        webhook: {
          select: {
            id: true,
            name: true,
            url: true,
            verified: true,
            webhookSecret: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const result = aliases.map(alias => ({
      id: alias.id,
      email: alias.email,
      active: alias.active,
      createdAt: alias.createdAt.toISOString(),
      updatedAt: alias.updatedAt.toISOString(),
      webhook: {
        id: alias.webhook.id,
        name: alias.webhook.name,
        url: alias.webhook.url,
        verified: alias.webhook.verified,
        hasSecret: !!alias.webhook.webhookSecret
      }
    }));

    return {
      aliases: result,
      domain: {
        id: domain.id,
        domain: domain.domain,
        verified: domain.verified
      },
      total: result.length
    };
  }
}
